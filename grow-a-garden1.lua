local Rayfield = loadstring(game:HttpGet('https://sirius.menu/rayfield'))()

-- Add required services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local GameEvents = ReplicatedStorage.GameEvents

-- Seed stock variables
local SeedStock = {}
local SelectedSeedStock = { Selected = {} } -- Changed to table for multiple seeds
local lastStockCheck = {}
local autoBuyEnabled = false
local stockCheckConnection = nil

-- Gear stock variables
local GearStock = {}
local SelectedGearStock = { Selected = {} } -- Changed to table for multiple gears
local lastGearStockCheck = {}
local gearAutoBuyEnabled = false
local gearStockCheckConnection = nil

-- Egg stock variables
local EggStock = {}
local SelectedEggStock = { Selected = {} } -- Changed to table for multiple eggs
local lastEggStockCheck = {}
local eggAutoBuyEnabled = false
local eggStockCheckConnection = nil

-- Function to get seed stock
local function GetSeedStock(IgnoreNoStock)
	local SeedShop = PlayerGui.Seed_Shop
	local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame.Stock_Text.Text
		local StockCount = tonumber(StockText:match("%d+"))

		--// Seperate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		SeedStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or SeedStock
end

-- Function to buy seed
local function BuySeed(Seed: string)
	GameEvents.BuySeedStock:FireServer(Seed)
end

-- Function to get gear stock
local function GetGearStock(IgnoreNoStock)
	local GearShop = PlayerGui.Gear_Shop -- Assuming gear shop is named Gear_Shop
	if not GearShop then
		print("Gear_Shop not found in PlayerGui")
		return {}
	end

	-- Try to find gear items - assuming similar structure to seed shop
	local Items = GearShop:FindFirstChild("WateringCan")
	if not Items then
		-- Try to find any frame that contains gear items
		for _, child in pairs(GearShop:GetDescendants()) do
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		print("Could not find gear items container")
		return {}
	end

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame:FindFirstChild("Stock_Text")
		if not StockText then continue end

		local StockCount = tonumber(StockText.Text:match("%d+")) or 0

		--// Separate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		GearStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or GearStock
end

-- Function to buy gear
local function BuyGear(Gear: string)
	GameEvents.BuyGearStock:FireServer(Gear) -- Assuming similar event structure
end

-- Function to get egg stock
local function GetEggStock(IgnoreNoStock)
	-- Try to find egg shop by looking for CommonEgg
	local EggShop = nil

	-- Search through PlayerGui for egg shop containing CommonEgg
	for _, gui in pairs(PlayerGui:GetChildren()) do
		if gui:FindFirstChild("CommonEgg", true) then
			EggShop = gui
			break
		end
	end

	if not EggShop then
		print("Egg shop not found (no CommonEgg found)")
		return {}
	end

	-- Try to find egg items container
	local Items = EggShop:FindFirstChild("CommonEgg", true)
	if Items then
		Items = Items.Parent
	else
		-- Try to find any frame that contains egg items
		for _, child in pairs(EggShop:GetDescendants()) do
			if child:IsA("Frame") and child:FindFirstChild("Main_Frame") then
				Items = child.Parent
				break
			end
		end
	end

	if not Items then
		print("Could not find egg items container")
		return {}
	end

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame:FindFirstChild("Stock_Text")
		if not StockText then continue end

		local StockCount = tonumber(StockText.Text:match("%d+")) or 0

		--// Separate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		EggStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or EggStock
end

-- Function to buy egg
local function BuyEgg(Egg: string)
	GameEvents.BuyEggStock:FireServer(Egg) -- Assuming similar event structure
end

-- Function to buy all selected eggs
local function BuyAllSelectedEggs()
    local selectedEggs = SelectedEggStock.Selected

    print("BuyAllSelectedEggs called")
    print("Selected eggs type:", type(selectedEggs))

    -- Handle different types of selectedEggs
    if type(selectedEggs) == "table" then
        print("Selected eggs table:")
        for k, v in pairs(selectedEggs) do
            print("  ", k, "=", v)
        end
    else
        print("Selected eggs value:", selectedEggs)
    end

    -- Check if we have any selected eggs - handle both table and other formats
    local eggsToProcess = {}

    if type(selectedEggs) == "table" then
        -- If it's a table, check if it has values
        local hasEggs = false
        for k, v in pairs(selectedEggs) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(eggsToProcess, type(k) == "string" and k or v)
                hasEggs = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(eggsToProcess, v)
                hasEggs = true
            end
        end

        if not hasEggs then
            print("No eggs selected (empty table)")
            return
        end
    else
        print("Selected eggs is not a table")
        return
    end

    print("Eggs to process:", eggsToProcess)

    -- Update stock before buying
    GetEggStock()

    -- Iterate through all selected eggs
    for _, eggName in pairs(eggsToProcess) do
        local stock = EggStock[eggName]
        print("Checking egg:", eggName, "Stock:", stock)

        -- Only buy if egg is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", eggName)
            for i = 1, stock do
                BuyEgg(eggName)
            end
        else
            print("Egg", eggName, "not in stock or stock is 0")
        end
    end
end

-- Function to buy all selected gears
local function BuyAllSelectedGears()
    local selectedGears = SelectedGearStock.Selected

    print("BuyAllSelectedGears called")
    print("Selected gears type:", type(selectedGears))

    -- Handle different types of selectedGears
    if type(selectedGears) == "table" then
        print("Selected gears table:")
        for k, v in pairs(selectedGears) do
            print("  ", k, "=", v)
        end
    else
        print("Selected gears value:", selectedGears)
    end

    -- Check if we have any selected gears - handle both table and other formats
    local gearsToProcess = {}

    if type(selectedGears) == "table" then
        -- If it's a table, check if it has values
        local hasGears = false
        for k, v in pairs(selectedGears) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(gearsToProcess, type(k) == "string" and k or v)
                hasGears = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(gearsToProcess, v)
                hasGears = true
            end
        end

        if not hasGears then
            print("No gears selected (empty table)")
            return
        end
    else
        print("Selected gears is not a table")
        return
    end

    print("Gears to process:", gearsToProcess)

    -- Update stock before buying
    GetGearStock()

    -- Iterate through all selected gears
    for _, gearName in pairs(gearsToProcess) do
        local stock = GearStock[gearName]
        print("Checking gear:", gearName, "Stock:", stock)

        -- Only buy if gear is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", gearName)
            for i = 1, stock do
                BuyGear(gearName)
            end
        else
            print("Gear", gearName, "not in stock or stock is 0")
        end
    end
end

-- Function to buy all selected seeds
local function BuyAllSelectedSeeds()
    local selectedSeeds = SelectedSeedStock.Selected

    print("BuyAllSelectedSeeds called")
    print("Selected seeds type:", type(selectedSeeds))

    -- Handle different types of selectedSeeds
    if type(selectedSeeds) == "table" then
        print("Selected seeds table:")
        for k, v in pairs(selectedSeeds) do
            print("  ", k, "=", v)
        end
    else
        print("Selected seeds value:", selectedSeeds)
    end

    -- Check if we have any selected seeds - handle both table and other formats
    local seedsToProcess = {}

    if type(selectedSeeds) == "table" then
        -- If it's a table, check if it has values
        local hasSeeds = false
        for k, v in pairs(selectedSeeds) do
            if v == true or (type(v) == "string" and v ~= "") then
                table.insert(seedsToProcess, type(k) == "string" and k or v)
                hasSeeds = true
            elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                table.insert(seedsToProcess, v)
                hasSeeds = true
            end
        end

        if not hasSeeds then
            print("No seeds selected (empty table)")
            return
        end
    else
        print("Selected seeds is not a table")
        return
    end

    print("Seeds to process:", seedsToProcess)

    -- Update stock before buying
    GetSeedStock()

    -- Iterate through all selected seeds
    for _, seedName in pairs(seedsToProcess) do
        local stock = SeedStock[seedName]
        print("Checking seed:", seedName, "Stock:", stock)

        -- Only buy if seed is in stock
        if stock and stock > 0 then
            print("Buying", stock, "of", seedName)
            for i = 1, stock do
                BuySeed(seedName)
            end
        else
            print("Seed", seedName, "not in stock or stock is 0")
        end
    end
end

-- Function to check stock changes and auto-buy
local function startStockChecking()
    stockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not autoBuyEnabled or not SelectedSeedStock.Selected then return end

        -- Check if we have selected seeds
        local hasSelectedSeeds = false
        if type(SelectedSeedStock.Selected) == "table" then
            for k, v in pairs(SelectedSeedStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedSeeds = true
                    break
                end
            end
        end

        if not hasSelectedSeeds then return end

        local currentStock = GetSeedStock()
        local selectedSeeds = SelectedSeedStock.Selected
        local shouldBuy = false

        -- Process selected seeds based on their format
        local seedsToCheck = {}
        if type(selectedSeeds) == "table" then
            for k, v in pairs(selectedSeeds) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local seedName = type(k) == "string" and k or v
                    if type(seedName) == "string" and seedName ~= "" then
                        table.insert(seedsToCheck, seedName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(seedsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected seed OR if we haven't checked before
        for _, seedName in pairs(seedsToCheck) do
            local previousStock = lastStockCheck[seedName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[seedName] then
                lastStockCheck[seedName] = currentStock[seedName]
                print("Stock changed for " .. seedName .. ": " .. (currentStock[seedName] or 0))

                -- If any selected seed is available, trigger purchase
                if currentStock[seedName] and currentStock[seedName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. seedName .. " - Stock: " .. currentStock[seedName])
                end
            end
        end

        -- Buy all available selected seeds if any stock changed
        if shouldBuy then
            print("Triggering purchase for available seeds")
            BuyAllSelectedSeeds()
        end
    end)
end

-- Function to stop stock checking
local function stopStockChecking()
    if stockCheckConnection then
        stockCheckConnection:Disconnect()
        stockCheckConnection = nil
    end
end

-- Function to check gear stock changes and auto-buy
local function startGearStockChecking()
    gearStockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not gearAutoBuyEnabled or not SelectedGearStock.Selected then return end

        -- Check if we have selected gears
        local hasSelectedGears = false
        if type(SelectedGearStock.Selected) == "table" then
            for k, v in pairs(SelectedGearStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedGears = true
                    break
                end
            end
        end

        if not hasSelectedGears then return end

        local currentStock = GetGearStock()
        local selectedGears = SelectedGearStock.Selected
        local shouldBuy = false

        -- Process selected gears based on their format
        local gearsToCheck = {}
        if type(selectedGears) == "table" then
            for k, v in pairs(selectedGears) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local gearName = type(k) == "string" and k or v
                    if type(gearName) == "string" and gearName ~= "" then
                        table.insert(gearsToCheck, gearName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(gearsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected gear OR if we haven't checked before
        for _, gearName in pairs(gearsToCheck) do
            local previousStock = lastGearStockCheck[gearName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[gearName] then
                lastGearStockCheck[gearName] = currentStock[gearName]
                print("Gear stock changed for " .. gearName .. ": " .. (currentStock[gearName] or 0))

                -- If any selected gear is available, trigger purchase
                if currentStock[gearName] and currentStock[gearName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. gearName .. " - Stock: " .. currentStock[gearName])
                end
            end
        end

        -- Buy all available selected gears if any stock changed
        if shouldBuy then
            print("Triggering purchase for available gears")
            BuyAllSelectedGears()
        end
    end)
end

-- Function to stop gear stock checking
local function stopGearStockChecking()
    if gearStockCheckConnection then
        gearStockCheckConnection:Disconnect()
        gearStockCheckConnection = nil
    end
end

-- Function to check egg stock changes and auto-buy
local function startEggStockChecking()
    eggStockCheckConnection = game:GetService("RunService").Heartbeat:Connect(function()
        if not eggAutoBuyEnabled or not SelectedEggStock.Selected then return end

        -- Check if we have selected eggs
        local hasSelectedEggs = false
        if type(SelectedEggStock.Selected) == "table" then
            for k, v in pairs(SelectedEggStock.Selected) do
                if v == true or (type(v) == "string" and v ~= "") then
                    hasSelectedEggs = true
                    break
                end
            end
        end

        if not hasSelectedEggs then return end

        local currentStock = GetEggStock()
        local selectedEggs = SelectedEggStock.Selected
        local shouldBuy = false

        -- Process selected eggs based on their format
        local eggsToCheck = {}
        if type(selectedEggs) == "table" then
            for k, v in pairs(selectedEggs) do
                if v == true or (type(v) == "string" and v ~= "") then
                    local eggName = type(k) == "string" and k or v
                    if type(eggName) == "string" and eggName ~= "" then
                        table.insert(eggsToCheck, eggName)
                    end
                elseif type(k) == "number" and type(v) == "string" and v ~= "" then
                    table.insert(eggsToCheck, v)
                end
            end
        end

        -- Check if stock has changed for any selected egg OR if we haven't checked before
        for _, eggName in pairs(eggsToCheck) do
            local previousStock = lastEggStockCheck[eggName] or -1 -- Use -1 if never checked before

            if previousStock ~= currentStock[eggName] then
                lastEggStockCheck[eggName] = currentStock[eggName]
                print("Egg stock changed for " .. eggName .. ": " .. (currentStock[eggName] or 0))

                -- If any selected egg is available, trigger purchase
                if currentStock[eggName] and currentStock[eggName] > 0 then
                    shouldBuy = true
                    print("Will buy " .. eggName .. " - Stock: " .. currentStock[eggName])
                end
            end
        end

        -- Buy all available selected eggs if any stock changed
        if shouldBuy then
            print("Triggering purchase for available eggs")
            BuyAllSelectedEggs()
        end
    end)
end

-- Function to stop egg stock checking
local function stopEggStockChecking()
    if eggStockCheckConnection then
        eggStockCheckConnection:Disconnect()
        eggStockCheckConnection = nil
    end
end

local Window = Rayfield:CreateWindow({
    Name = "หมา " .. 1.0,
    LoadingTitle = "by XZery",
    LoadingSubtitle = "Loading...",
    ConfigurationSaving = {
        Enabled = true,
        FolderName = "RayfieldScriptHub",
        FileName = "grow-a-garden"
    },
    Discord = {
        Enabled = false,
        Invite = "noinvitelink",
        RememberJoins = true
    },
    KeySystem = false,
    KeySettings = {
        Title = "Untitled",
        Subtitle = "Key System",
        Note = "No method of obtaining the key is provided",
        FileName = "Key",
        SaveKey = true,
        GrabKeyFromSite = false,
        Key = {"Hello"}
    }
})

local Tabs = {
    Main = Window:CreateTab("Main", 4483362458),
    Seed = Window:CreateTab("Seed", 4483362458),
    Gear = Window:CreateTab("Gear", 4483362458),
    Egg = Window:CreateTab("Egg", 4483362458)
}

local speed = Tabs.Main:CreateSlider({
    Name = "Speed",
    Range = {16, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 16,
    Flag = "SpeedSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.WalkSpeed = Value
        end
    end,
})

local jump = Tabs.Main:CreateSlider({
    Name = "Jump Power",
    Range = {50, 200},
    Increment = 1,
    Suffix = "",
    CurrentValue = 50,
    Flag = "JumpSlider",
    Callback = function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        if char and char:FindFirstChild("Humanoid") then
            char.Humanoid.JumpPower = Value
        end
    end,
})

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:CreateToggle({
        Name = "Fly",
        CurrentValue = false,
        Flag = "FlyToggle",
        Callback = function(Value)
            flyEnabled = Value
            if flyEnabled then
                enableFly()
            else
                disableFly()
            end
        end,
    })

    -- Reset Character Button
    local resetButton = Tabs.Main:CreateButton({
        Name = "Reset Character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end,
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:CreateToggle({
        Name = "Anti-AFK",
        CurrentValue = false,
        Flag = "AntiAfkToggle",
        Callback = function(Value)
            antiAfkEnabled = Value
            if antiAfkEnabled then
                startAntiAfk()
            else
                stopAntiAfk()
            end
        end,
    })

    -- Seed stock dropdown (multi-select) - moved to Seed tab
    local seedDropdown = Tabs.Seed:CreateDropdown({
        Name = "Select Seeds",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "SeedDropdown",
        Callback = function(Value)
            print("Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedSeedStock.Selected = Value -- Value is now a table of selected seeds
            -- Update stock data when selection changes
            GetSeedStock()
        end,
    })

    -- Function to update seed dropdown
    local function updateSeedDropdown()
        local allSeeds = GetSeedStock(false) -- Get all seeds regardless of stock
        local seedList = {}

        for seedName, _ in pairs(allSeeds) do
            table.insert(seedList, seedName)
        end

        seedDropdown:Refresh(seedList, true)
    end

    -- Auto-buy toggle - moved to Seed tab
    local autoBuyToggle = Tabs.Seed:CreateToggle({
        Name = "Auto-Buy Seeds",
        CurrentValue = false,
        Flag = "AutoBuyToggle",
        Callback = function(Value)
            autoBuyEnabled = Value
            if autoBuyEnabled then
                startStockChecking()
            else
                stopStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above



    -- Initial seed dropdown update
    updateSeedDropdown()

    -- Load saved seed selections (will be loaded after SaveManager is properly initialized)

    -- Gear stock dropdown (multi-select)
    local gearDropdown = Tabs.Gear:CreateDropdown({
        Name = "Select Gears",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "GearDropdown",
        Callback = function(Value)
            print("Gear Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedGearStock.Selected = Value -- Value is now a table of selected gears
            -- Update stock data when selection changes
            GetGearStock()
        end,
    })

    -- Function to update gear dropdown
    local function updateGearDropdown()
        local allGears = GetGearStock(false) -- Get all gears regardless of stock
        local gearList = {}

        for gearName, _ in pairs(allGears) do
            table.insert(gearList, gearName)
        end

        gearDropdown:Refresh(gearList, true)
    end

    -- Gear auto-buy toggle
    local gearAutoBuyToggle = Tabs.Gear:CreateToggle({
        Name = "Auto-Buy Gears",
        CurrentValue = false,
        Flag = "GearAutoBuyToggle",
        Callback = function(Value)
            gearAutoBuyEnabled = Value
            if gearAutoBuyEnabled then
                startGearStockChecking()
            else
                stopGearStockChecking()
            end
        end,
    })

    -- Note: Callback is now handled in the dropdown creation above

    -- Initial gear dropdown update
    updateGearDropdown()

    -- Egg stock dropdown (multi-select)
    local eggDropdown = Tabs.Egg:CreateDropdown({
        Name = "Select Eggs",
        Options = {},
        CurrentOption = {},
        MultipleOptions = true,
        Flag = "EggDropdown",
        Callback = function(Value)
            print("Egg Dropdown OnChanged called")
            print("Value type:", type(Value))

            if type(Value) == "table" then
                print("Value contents:")
                for k, v in pairs(Value) do
                    print("  ", k, "=", v, "(", type(k), "->", type(v), ")")
                end
            else
                print("Value:", Value)
            end

            SelectedEggStock.Selected = Value -- Value is now a table of selected eggs
            -- Update stock data when selection changes
            GetEggStock()
        end,
    })

    -- Function to update egg dropdown
    local function updateEggDropdown()
        local allEggs = GetEggStock(false) -- Get all eggs regardless of stock
        local eggList = {}

        for eggName, _ in pairs(allEggs) do
            table.insert(eggList, eggName)
        end

        eggDropdown:Refresh(eggList, true)
    end

    -- Egg auto-buy toggle
    local eggAutoBuyToggle = Tabs.Egg:CreateToggle({
        Name = "Auto-Buy Eggs",
        CurrentValue = false,
        Flag = "EggAutoBuyToggle",
        Callback = function(Value)
            eggAutoBuyEnabled = Value
            if eggAutoBuyEnabled then
                startEggStockChecking()
            else
                stopEggStockChecking()
            end
        end,
    })

    -- Initial egg dropdown update
    updateEggDropdown()

-- Rayfield has built-in configuration saving, so we don't need separate SaveManager/InterfaceManager

Rayfield:Notify({
    Title = "Script Loaded",
    Content = "The script has been loaded successfully.",
    Duration = 6.5,
    Image = 4483362458,
    Actions = {
        Ignore = {
            Name = "Okay!",
            Callback = function()
                print("The user tapped Okay!")
            end
        },
    },
})